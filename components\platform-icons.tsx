export const PlatformIcons = {
  google: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
    </svg>
  ),
  youtube: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#FF0000">
      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
    </svg>
  ),
  bing: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#008373">
      <path d="M5.71 2l3.637 1.315-.082 14.798 5.97-3.475-2.808-1.323-1.896-3.87 8.765 3.147v4.01l-9.96 5.398L5.71 2"/>
    </svg>
  ),
  amazon: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#FF9900">
      <path d="M6.763 12.896c0-1.416.432-2.574 1.296-3.474.864-.9 1.944-1.35 3.24-1.35 1.296 0 2.376.45 3.24 1.35.864.9 1.296 2.058 1.296 3.474 0 1.416-.432 2.574-1.296 3.474-.864.9-1.944 1.35-3.24 1.35-1.296 0-2.376-.45-3.24-1.35-.864-.9-1.296-2.058-1.296-3.474zm2.592 0c0 .792.24 1.44.72 1.944.48.504 1.08.756 1.8.756.72 0 1.32-.252 1.8-.756.48-.504.72-1.152.72-1.944 0-.792-.24-1.44-.72-1.944-.48-.504-1.08-.756-1.8-.756-.72 0-1.32.252-1.8.756-.48.504-.72 1.152-.72 1.944z"/>
    </svg>
  ),
  ebay: (
    <svg className="w-4 h-4" viewBox="0 0 24 24">
      <path d="M0 11.47V8.64h4.06c2.93 0 4.33 1.11 4.33 3.49 0 2.14-1.46 3.47-4.12 3.47H0v-4.13zm2.57 2.41h.86c1.48 0 2.18-.62 2.18-1.86 0-1.24-.7-1.86-2.18-1.86h-.86v3.72z" fill="#E53238"/>
      <path d="M9.47 8.64h2.79l1.25 4.78h.02l1.24-4.78h2.79v7.46h-2.04v-5.44h-.02l-1.58 5.44h-1.67l-1.58-5.44h-.02v5.44H9.47V8.64z" fill="#0064D2"/>
      <path d="M19.12 8.64h2.57v7.46h-2.57V8.64z" fill="#F5AF02"/>
      <path d="M22.41 8.64H24v7.46h-1.59V8.64z" fill="#86B817"/>
    </svg>
  ),
  appstore: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#0D96F6">
      <path d="M12 2C6.477 2 2 6.477 2 12c0 5.523 4.477 10 10 10s10-4.477 10-10c0-5.523-4.477-10-10-10zm5.5 15.5h-11v-11h11v11z"/>
    </svg>
  ),
  playstore: (
    <svg className="w-4 h-4" viewBox="0 0 24 24">
      <path d="M3.609 1.814L13.792 12 3.61 22.186c-.28.28-.657.423-1.033.423C1.82 22.609 1 21.789 1 20.033V3.967c0-1.756.82-2.576 2.576-2.576.376 0 .753.143 1.033.423z" fill="#4CAF50"/>
      <path d="M13.792 12L3.61 1.814 17.186 8.44 13.793 12z" fill="#FFC107"/>
      <path d="M17.186 8.44L3.61 22.186l13.576-6.627L13.793 12l3.393-3.56z" fill="#F44336"/>
      <path d="M22.1 12.5L17.186 8.44l-3.393 3.56 3.393 3.56L22.1 11.5c.5-.3.5-.7 0-1z" fill="#DD2C00"/>
    </svg>
  ),
  instagram: (
    <svg className="w-4 h-4" viewBox="0 0 24 24">
      <defs>
        <radialGradient id="instagramGradient" cx="30%" cy="107%" r="150%">
          <stop offset="0%" stopColor="#fdf497"/>
          <stop offset="5%" stopColor="#fdf497"/>
          <stop offset="45%" stopColor="#fd5949"/>
          <stop offset="60%" stopColor="#d6249f"/>
          <stop offset="90%" stopColor="#285AEB"/>
        </radialGradient>
      </defs>
      <path fill="url(#instagramGradient)" d="M12 2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153.509.5.902 1.105 1.153 1.772.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 01-1.153 1.772c-.5.508-1.105.902-1.772 1.153-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 01-1.772-1.153 4.904 4.904 0 01-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 011.153-1.772A4.897 4.897 0 015.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2zm0 5a5 5 0 100 10 5 5 0 000-10zm6.5-.25a1.25 1.25 0 10-2.5 0 1.25 1.25 0 002.5 0zM12 9a3 3 0 110 6 3 3 0 010-6z"/>
    </svg>
  ),
  twitter: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#000000">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>
  ),
  pinterest: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#E60023">
      <path d="M12 2C6.477 2 2 6.477 2 12c0 4.237 2.636 7.855 6.356 9.312-.088-.791-.167-2.005.035-2.868.181-.78 1.172-4.97 1.172-4.97s-.299-.6-.299-1.486c0-1.39.806-2.428 1.81-2.428.852 0 1.264.64 1.264 1.408 0 .858-.546 2.14-.828 3.33-.236.995.5 1.807 1.48 1.807 1.778 0 3.144-1.874 3.144-4.58 0-2.393-1.72-4.068-4.177-4.068-2.845 0-4.515 2.135-4.515 4.34 0 .859.331 1.781.745 2.281a.3.3 0 01.069.288l-.278 1.133c-.044.183-.145.223-.335.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.052 6.608-6.052 3.469 0 6.165 2.473 6.165 5.776 0 3.447-2.173 6.22-5.19 6.22-1.013 0-1.965-.525-2.291-1.148l-.623 2.378c-.226.869-.835 1.958-1.244 2.621.937.29 1.931.446 2.962.446 5.523 0 10-4.477 10-10S17.523 2 12 2z"/>
    </svg>
  ),
  etsy: (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="#F45800">
      <path d="M8.954 3.667c0-.14-.01-.28-.029-.417h6.158c.826 0 1.34.15 1.652.51.208.25.347.63.347 1.14 0 .18-.01.44-.03.78l-.05.71c-.04.53-.06.84-.06.93 0 .14.05.24.15.29.1.06.3.08.59.08h.2v1.06h-3.46v-.99c.48-.04.77-.1.87-.17.11-.07.17-.19.17-.35 0-.11-.02-.35-.05-.73l-.07-.86c-.05-.62-.17-1.04-.37-1.27-.2-.22-.52-.34-.97-.34h-.94v7.32c0 .61.05 1 .15 1.18.1.18.31.29.63.33.15.02.47.03.95.03v1.01h-5.07v-1.01c.49 0 .81-.01.96-.03.32-.04.53-.15.63-.33.1-.18.15-.57.15-1.18v-7.32h-.93c-.45 0-.77.12-.97.34-.2.23-.32.65-.37 1.27l-.07.86c-.03.38-.05.62-.05.73 0 .16.06.28.17.35.1.07.39.13.87.17v.99h-3.46v-1.06h.2c.29 0 .49-.02.59-.08.1-.05.15-.15.15-.29 0-.09-.02-.4-.06-.93l-.05-.71c-.02-.34-.03-.6-.03-.78 0-.51.14-.89.35-1.14.31-.36.82-.51 1.65-.51h6.16c-.02.137-.03.277-.03.417z"/>
    </svg>
  ),
  tiktok: (
    <svg className="w-4 h-4" viewBox="0 0 24 24">
      <path d="M16.6 5.82s.51-.5 0 0A4.278 4.278 0 0120 10.1v2.08c-.94-.21-1.92-.32-2.92-.32-2.69 0-5.14 1.28-6.72 3.33.02-.16.04-.32.04-.48V4.1h3.46a4.28 4.28 0 002.74 1.72zm-2.77-2.74h-4.88v11.11c0 3.05-2.47 5.52-5.52 5.52-.11 0-.23 0-.34-.01a5.53 5.53 0 004.71-5.51c0-.16-.02-.32-.04-.48 1.58-2.05 4.03-3.33 6.72-3.33 1 0 1.98.11 2.92.32V6.92c-.86-.24-1.76-.38-2.69-.38-1.93 0-3.71.78-5 2.05V3.08h4.88c.09.61.13 1.24.13 1.89 0 .65-.04 1.28-.13 1.89z" fill="#000000"/>
      <path d="M16.6 5.82s.51-.5 0 0z" fill="#00F2EA"/>
    </svg>
  ),
  trends: (
    <svg className="w-4 h-4" viewBox="0 0 24 24">
      <path d="M7.5 4.5v15M10.5 4.5l6 15M16.5 4.5h-12" stroke="#4285F4" strokeWidth="2"/>
      <path d="M4.5 19.5h12M13.5 4.5l-6 15" stroke="#34A853" strokeWidth="2"/>
    </svg>
  ),
}; 