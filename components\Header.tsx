"use client";
import Link from "next/link";
import React, { useState } from "react";
import { useU<PERSON>, SignInButton, SignUpButton, UserButton, useClerk } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle
} from "@/components/ui/navigation-menu";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isSignedIn, user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();

  const handleScroll = (
    e: React.MouseEvent<HTMLAnchorElement>,
    targetId: string
  ) => {
    e.preventDefault();
    const element = document.getElementById(targetId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setIsOpen(false); // Close menu after clicking a link
    }
  };

  return (
    <header className="px-8 bg-indigo-900 py-4 shadow-lg sticky top-0 z-50">
      <div className="container mx-auto flex justify-between items-center">
        <Link
          href="/"
          className="text-white text-2xl font-bold hover:text-indigo-200 transition-colors"
        >
          <h1 className="text-center text-xl font-bold tracking-wider text-white ">
            <span
              className="inline-block px-1 font-extrabold text-xs md:text-base"
              style={{
                textShadow:
                  "-1px 0 #4338ca, 0 1px #4338ca, 1px 0 #4338ca, 0 -1px #4338ca",
              }}
            >
              FREE SEO AUDIT TOOL
            </span>
          </h1>
        </Link>
        <button
          className="md:hidden text-white focus:outline-none"
          onClick={() => setIsOpen(!isOpen)}
          aria-label="Toggle menu"
        >
          <svg
            className="h-6 w-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            {isOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>
        <nav
          className={`fixed md:relative top-[50px] md:top-0 left-0 w-full md:w-auto bg-indigo-900 md:bg-transparent transition-all duration-300 ease-in-out ${
            isOpen
              ? "opacity-100 translate-y-0 visible"
              : "opacity-0 -translate-y-full invisible md:visible md:opacity-100 md:translate-y-0"
          } md:block`}
        >
          <NavigationMenu>
            <NavigationMenuList>
              <ul className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 p-4 md:p-0 items-center">
                <li>
                  <Link
                    href="/"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={(e) => {
                      setIsOpen(false);
                    }}
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href="/keyword-research"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={() => setIsOpen(false)}
                  >
                    Keyword Research Tool
                  </Link>
                </li>
                <li>
                  <Link
                    href="/meta-tags-generator"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={() => setIsOpen(false)}
                  >
                    Meta Tags Generator
                  </Link>
                </li>
                <li>
                  <Link
                    href="/open-graph-generator"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={() => setIsOpen(false)}
                  >
                    Open Graph Generator
                  </Link>
                </li>
                <li>
                  <Link
                    href="/campaign-url-builder"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={() => setIsOpen(false)}
                  >
                    Campaign URL Builder
                  </Link>
                </li>
                <li>
                  <Link
                    href="/freelance-services"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={() => setIsOpen(false)}
                  >
                    Freelance 
                  </Link>
                </li>
                <li>
                  <a
                    href="#about"
                    className="block text-indigo-200 hover:text-white transition-colors text-[13px]"
                    onClick={(e) => handleScroll(e, "about")}
                  >
                    About
                  </a>
                </li>
                {!isSignedIn ? (
                  <>
                    <li>
                      <SignInButton mode="modal">
                        <button className="block text-indigo-200 hover:text-white transition-colors text-[13px] bg-transparent border border-indigo-200 px-4 py-1 rounded-md hover:bg-indigo-200 hover:text-indigo-900">
                          Login
                        </button>
                      </SignInButton>
                    </li>
                    <li>
                      <SignUpButton mode="modal">
                        <button className="block text-indigo-900 bg-indigo-200 hover:bg-white transition-colors text-[14px] px-4 py-1 rounded-md">
                          Sign Up
                        </button>
                      </SignUpButton>
                    </li>
                  </>
                ) : (
                  <>
                    <li>
                      <Link
                        href="/dashboard"
                        className="block text-indigo-200 hover:text-white transition-colors text-[13px] mr-4"
                        onClick={() => setIsOpen(false)}
                      >
                        Dashboard
                      </Link>
                    </li>
                    <li>
                      <button
                        onClick={() => signOut()}
                        className="block text-indigo-200 hover:text-white transition-colors text-[13px] mr-4"
                      >
                        Logout
                      </button>
                    </li>
                    <li>
                      <UserButton afterSignOutUrl="/" />
                    </li>
                  </>
                )}
              </ul>
            </NavigationMenuList>
          </NavigationMenu>
        </nav>
      </div>
    </header>
  );
};

export default Header;
