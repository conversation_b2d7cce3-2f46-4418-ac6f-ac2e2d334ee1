generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String               @id(map: "User_new_pkey") @default(cuid())
  name                String?
  email               String?              @unique
  emailVerified       DateTime?
  image               String?
  isPremium           Boolean              @default(false)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  clerkId             String?              @unique
  SEOToolResult       SEOToolResult[]
  siteAudits          SiteAudit[]
  keywordTracking     KeywordTracking[]
  competitorAnalysis  CompetitorAnalysis[]
}

model ServiceRequest {
  id                     String               @id @default(cuid())
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  question               String
  websiteType            String
  reference              String?
  name                   String
  phoneNumber            String
  email                  String
  brandName              String
  brandAge               String
  brandDescription       String
  hasDomain              Boolean
  domainName             String?
  websitesUnderDomain    String?
  hasHosting             Boolean
  needsLogo              Boolean
  categoryPages          String
  needsContent           Boolean
  providingProductImages Boolean
  additionalRequirements String?
  budget                 String
  status                 ServiceRequestStatus @default(PENDING)

  @@index([email])
  @@index([status])
  @@index([createdAt])
}

model Account {
  id                String  @id
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  @@unique([provider, providerAccountId])
}

model ContactMessage {
  id        Int      @id @default(autoincrement())
  fullName  String
  email     String
  message   String
  createdAt DateTime @default(now())
}

model ContactRequest {
  id        String   @id
  userId    String
  name      String
  email     String
  phone     String?
  service   String
  message   String
  status    String   @default("PENDING")
  createdAt DateTime @default(now())
  updatedAt DateTime

  @@index([userId])
}

model Domain {
  id         Int      @id @default(autoincrement())
  domainName String   @unique
  ipAddress  String?
  pdfData    Bytes?
  pdfPath    String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime
  userId     String?
}

model KeywordSearch {
  id         Int      @id @default(autoincrement())
  keyword    String
  platform   String
  searchType String
  country    String
  language   String
  createdAt  DateTime @default(now())
  results    Json
  userId     String?
}

model SEOToolResult {
  id        String      @id
  userId    String
  toolType  SEOToolType
  url       String
  result    Json
  createdAt DateTime    @default(now())
  updatedAt DateTime
  User      User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([toolType])
  @@index([userId])
}

model Session {
  id           String   @id
  sessionToken String   @unique
  userId       String
  expires      DateTime
}

model UserUsage {
  id               Int      @id @default(autoincrement())
  userId           String   @unique
  freeReportsUsed  Int      @default(0)
  freeSearchesUsed Int      @default(0)
  lastResetDate    DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserRole {
  FREELANCER
  CLIENT
}

enum ProjectStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum ServiceRequestStatus {
  PENDING
  CONTACTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum SEOToolType {
  META_TAGS
  KEYWORD_DENSITY
  HEADER_TAGS
  INTERNAL_LINKS
  ALT_TEXT
  BROKEN_LINKS
  ROBOTS_TXT
  SITEMAP
  SEO_SCORE
  READABILITY
  TITLE_PREVIEW
  DESCRIPTION_PREVIEW
  STRUCTURED_DATA
  CANONICAL_URL
  MOBILE_FRIENDLY
  FULL_AUDIT
  ACCESSIBILITY_AUDIT
  PERFORMANCE_AUDIT
  COMPETITOR_ANALYSIS
}

model SiteAudit {
  id                    String              @id @default(cuid())
  userId                String
  url                   String
  status                AuditStatus         @default(PENDING)
  digitalCertaintyIndex Int?                @default(0)
  seoScore             Int?                @default(0)
  accessibilityScore   Int?                @default(0)
  performanceScore     Int?                @default(0)
  qualityScore         Int?                @default(0)
  totalPages           Int?                @default(0)
  crawledPages         Int?                @default(0)
  totalIssues          Int?                @default(0)
  criticalIssues       Int?                @default(0)
  warningIssues        Int?                @default(0)
  infoIssues           Int?                @default(0)
  startedAt            DateTime?
  completedAt          DateTime?
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  user                 User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  auditResults         AuditResult[]
  auditIssues          AuditIssue[]
  auditReports         AuditReport[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model AuditResult {
  id          String    @id @default(cuid())
  auditId     String
  category    String    // SEO, Accessibility, Performance, Quality
  subcategory String    // Meta tags, Images, Links, etc.
  score       Int       @default(0)
  maxScore    Int       @default(100)
  data        Json
  createdAt   DateTime  @default(now())
  audit       SiteAudit @relation(fields: [auditId], references: [id], onDelete: Cascade)

  @@index([auditId])
  @@index([category])
}

model AuditIssue {
  id          String      @id @default(cuid())
  auditId     String
  severity    IssueSeverity
  category    String
  title       String
  description String
  pageUrl     String?
  element     String?
  suggestion  String?
  isFixed     Boolean     @default(false)
  createdAt   DateTime    @default(now())
  audit       SiteAudit   @relation(fields: [auditId], references: [id], onDelete: Cascade)

  @@index([auditId])
  @@index([severity])
  @@index([category])
}

model AuditReport {
  id          String      @id @default(cuid())
  auditId     String
  title       String
  description String?
  reportType  ReportType
  format      ReportFormat
  filePath    String?
  isPublic    Boolean     @default(false)
  createdAt   DateTime    @default(now())
  audit       SiteAudit   @relation(fields: [auditId], references: [id], onDelete: Cascade)

  @@index([auditId])
  @@index([reportType])
}

model KeywordTracking {
  id            String   @id @default(cuid())
  userId        String
  keyword       String
  targetUrl     String
  currentRank   Int?
  previousRank  Int?
  searchVolume  Int?
  difficulty    Int?
  country       String   @default("US")
  language      String   @default("en")
  isTracking    Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([keyword])
  @@index([isTracking])
}

model CompetitorAnalysis {
  id               String   @id @default(cuid())
  userId           String
  primaryDomain    String
  competitorDomain String
  analysisData     Json
  createdAt        DateTime @default(now())
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([primaryDomain])
}

enum AuditStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum IssueSeverity {
  CRITICAL
  WARNING
  INFO
}

enum ReportType {
  FULL_AUDIT
  SEO_SUMMARY
  ACCESSIBILITY_SUMMARY
  PERFORMANCE_SUMMARY
  CUSTOM
}

enum ReportFormat {
  PDF
  CSV
  JSON
  HTML
}
