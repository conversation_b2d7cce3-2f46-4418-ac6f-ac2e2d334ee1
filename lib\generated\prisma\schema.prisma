// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "../lib/generated/prisma"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js Models (required for PrismaAdapter)
model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?  @db.Text
  access_token       String?  @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?  @db.Text
  session_state      String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User model with custom fields
model User {
  id              String          @id @default(cuid())
  name            String?
  email           String?         @unique
  emailVerified   DateTime?
  image           String?
  accounts        Account[]
  sessions        Session[]
  
  // Custom fields
  isPremium       Boolean         @default(false)
  usage           UserUsage?
  domains         Domain[]
  keywordSearches KeywordSearch[]
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
}

model UserUsage {
  id               Int      @id @default(autoincrement())
  userId           String   @unique
  user             User     @relation(fields: [userId], references: [id])
  freeReportsUsed  Int      @default(0)
  freeSearchesUsed Int      @default(0)
  lastResetDate    DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model Domain {
  id         Int      @id @default(autoincrement())
  domainName String   @unique
  ipAddress  String?
  pdfData    Bytes?
  pdfPath    String?
  userId     String?
  user       User?    @relation(fields: [userId], references: [id])
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model KeywordSearch {
  id         Int      @id @default(autoincrement())
  keyword    String
  platform   String
  searchType String
  country    String
  language   String
  userId     String?
  user       User?    @relation(fields: [userId], references: [id])
  createdAt  DateTime @default(now())
  results    Json
}

model ContactMessage {
  id        Int      @id @default(autoincrement())
  fullName  String
  email     String
  message   String   @db.Text
  createdAt DateTime @default(now())
}
