export async function verifyRecaptcha(token: string): Promise<boolean> {
  // Bypass reCAPTCHA verification in development
  if (process.env.NODE_ENV === 'development') {
    return true;
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`,
    });

    const data = await response.json();
    return data.success && data.score >= 0.5; // Adjust score threshold as needed
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error);
    return false;
  }
} 