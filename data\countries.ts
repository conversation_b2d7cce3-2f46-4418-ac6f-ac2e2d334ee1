const countries = [
  { value: 'global', label: 'Global / Worldwide (All Countries)' },
  { value: 'af', label: 'Afghanistan' },
  { value: 'ax', label: 'Aland Islands' },
  { value: 'al', label: 'Albania' },
  { value: 'dz', label: 'Algeria' },
  { value: 'as', label: 'American Samoa' },
  { value: 'ad', label: 'Andorra' },
  { value: 'ao', label: 'Angola' },
  { value: 'ai', label: 'Anguilla' },
  { value: 'aq', label: 'Antarctica' },
  { value: 'ag', label: 'Antigua and Barbuda' },
  { value: 'ar', label: 'Argentina' },
  { value: 'am', label: 'Armenia' },
  { value: 'aw', label: 'Aruba' },
  { value: 'au', label: 'Australia' },
  { value: 'at', label: 'Austria' },
  { value: 'az', label: 'Azerbaijan' },
  { value: 'bs', label: 'Bahamas' },
  { value: 'bh', label: 'Bahrain' },
  { value: 'bd', label: 'Bangladesh' },
  { value: 'bb', label: 'Barbados' },
  { value: 'by', label: 'Belarus' },
  { value: 'be', label: 'Belgium' },
  { value: 'bz', label: 'Belize' },
  { value: 'bj', label: 'Benin' },
  { value: 'bm', label: 'Bermuda' },
  { value: 'bt', label: 'Bhutan' },
  { value: 'bo', label: 'Bolivia' },
  { value: 'ba', label: 'Bosnia and Herzegovina' },
  { value: 'bw', label: 'Botswana' },
  { value: 'bv', label: 'Bouvet Island' },
  { value: 'br', label: 'Brazil' },
  { value: 'io', label: 'British Indian Ocean Territory' },
  { value: 'vg', label: 'British Virgin Islands' },
  { value: 'bn', label: 'Brunei' },
  { value: 'bg', label: 'Bulgaria' },
  { value: 'bf', label: 'Burkina Faso' },
  { value: 'bi', label: 'Burundi' },
  { value: 'kh', label: 'Cambodia' },
  { value: 'cm', label: 'Cameroon' },
  { value: 'ca', label: 'Canada' },
  { value: 'cv', label: 'Cape Verde' },
  { value: 'ky', label: 'Cayman Islands' },
  { value: 'cf', label: 'Central African Republic' },
  { value: 'td', label: 'Chad' },
  { value: 'cl', label: 'Chile' },
  { value: 'cn', label: 'China' },
  { value: 'cx', label: 'Christmas Island' },
  { value: 'cc', label: 'Cocos Islands' },
  { value: 'co', label: 'Colombia' },
  { value: 'km', label: 'Comoros' },
  { value: 'ck', label: 'Cook Islands' },
  { value: 'cr', label: 'Costa Rica' },
  { value: 'hr', label: 'Croatia' },
  { value: 'cu', label: 'Cuba' },
  { value: 'cy', label: 'Cyprus' },
  { value: 'cz', label: 'Czech Republic' },
  { value: 'cd', label: 'Democratic Republic of the Congo' },
  { value: 'dk', label: 'Denmark' },
  { value: 'dj', label: 'Djibouti' },
  { value: 'dm', label: 'Dominica' },
  { value: 'do', label: 'Dominican Republic' },
  { value: 'ec', label: 'Ecuador' },
  { value: 'eg', label: 'Egypt' },
  { value: 'sv', label: 'El Salvador' },
  { value: 'gq', label: 'Equatorial Guinea' },
  { value: 'er', label: 'Eritrea' },
  { value: 'ee', label: 'Estonia' },
  { value: 'et', label: 'Ethiopia' },
  { value: 'fk', label: 'Falkland Islands' },
  { value: 'fo', label: 'Faroe Islands' },
  { value: 'fj', label: 'Fiji' },
  { value: 'fi', label: 'Finland' },
  { value: 'fr', label: 'France' },
  { value: 'gf', label: 'French Guiana' },
  { value: 'pf', label: 'French Polynesia' },
  { value: 'tf', label: 'French Southern Territories' },
  { value: 'ga', label: 'Gabon' },
  { value: 'gm', label: 'Gambia' },
  { value: 'ge', label: 'Georgia' },
  { value: 'de', label: 'Germany' },
  { value: 'gh', label: 'Ghana' },
  { value: 'gi', label: 'Gibraltar' },
  { value: 'gr', label: 'Greece' },
  { value: 'gl', label: 'Greenland' },
  { value: 'gd', label: 'Grenada' },
  { value: 'gp', label: 'Guadeloupe' },
  { value: 'gu', label: 'Guam' },
  { value: 'gt', label: 'Guatemala' },
  { value: 'gg', label: 'Guernsey' },
  { value: 'gn', label: 'Guinea' },
  { value: 'gw', label: 'Guinea-Bissau' },
  { value: 'gy', label: 'Guyana' },
  { value: 'ht', label: 'Haiti' },
  { value: 'hm', label: 'Heard Island and McDonald Islands' },
  { value: 'hn', label: 'Honduras' },
  { value: 'hk', label: 'Hong Kong' },
  { value: 'hu', label: 'Hungary' },
  { value: 'is', label: 'Iceland' },
  { value: 'in', label: 'India' },
  { value: 'id', label: 'Indonesia' },
  { value: 'ir', label: 'Iran' },
  { value: 'iq', label: 'Iraq' },
  { value: 'ie', label: 'Ireland' },
  { value: 'im', label: 'Isle of Man' },
  { value: 'il', label: 'Israel' },
  { value: 'it', label: 'Italy' },
  { value: 'ci', label: 'Ivory Coast' },
  { value: 'jm', label: 'Jamaica' },
  { value: 'jp', label: 'Japan' },
  { value: 'je', label: 'Jersey' },
  { value: 'jo', label: 'Jordan' },
  { value: 'kz', label: 'Kazakhstan' },
  { value: 'ke', label: 'Kenya' },
  { value: 'ki', label: 'Kiribati' },
  { value: 'xk', label: 'Kosovo' },
  { value: 'kw', label: 'Kuwait' },
  { value: 'kg', label: 'Kyrgyzstan' },
  { value: 'la', label: 'Laos' },
  { value: 'lv', label: 'Latvia' },
  { value: 'lb', label: 'Lebanon' },
  { value: 'ls', label: 'Lesotho' },
  { value: 'lr', label: 'Liberia' },
  { value: 'ly', label: 'Libya' },
  { value: 'li', label: 'Liechtenstein' },
  { value: 'lt', label: 'Lithuania' },
  { value: 'lu', label: 'Luxembourg' },
  { value: 'mo', label: 'Macao' },
  { value: 'mk', label: 'Macedonia' },
  { value: 'mg', label: 'Madagascar' },
  { value: 'mw', label: 'Malawi' },
  { value: 'my', label: 'Malaysia' },
  { value: 'mv', label: 'Maldives' },
  { value: 'ml', label: 'Mali' },
  { value: 'mt', label: 'Malta' },
  { value: 'mh', label: 'Marshall Islands' },
  { value: 'mq', label: 'Martinique' },
  { value: 'mr', label: 'Mauritania' },
  { value: 'mu', label: 'Mauritius' },
  { value: 'yt', label: 'Mayotte' },
  { value: 'mx', label: 'Mexico' },
  { value: 'fm', label: 'Micronesia' },
  { value: 'md', label: 'Moldova' },
  { value: 'mc', label: 'Monaco' },
  { value: 'mn', label: 'Mongolia' },
  { value: 'me', label: 'Montenegro' },
  { value: 'ms', label: 'Montserrat' },
  { value: 'ma', label: 'Morocco' },
  { value: 'mz', label: 'Mozambique' },
  { value: 'mm', label: 'Myanmar' },
  { value: 'na', label: 'Namibia' },
  { value: 'nr', label: 'Nauru' },
  { value: 'np', label: 'Nepal' },
  { value: 'nl', label: 'Netherlands' },
  { value: 'nc', label: 'New Caledonia' },
  { value: 'nz', label: 'New Zealand' },
  { value: 'ni', label: 'Nicaragua' },
  { value: 'ne', label: 'Niger' },
  { value: 'ng', label: 'Nigeria' },
  { value: 'nu', label: 'Niue' },
  { value: 'nf', label: 'Norfolk Island' },
  { value: 'mp', label: 'Northern Mariana Islands' },
  { value: 'kp', label: 'North Korea' },
  { value: 'no', label: 'Norway' },
  { value: 'om', label: 'Oman' },
  { value: 'pk', label: 'Pakistan' },
  { value: 'pw', label: 'Palau' },
  { value: 'ps', label: 'Palestinian Territory' },
  { value: 'pa', label: 'Panama' },
  { value: 'pg', label: 'Papua New Guinea' },
  { value: 'py', label: 'Paraguay' },
  { value: 'pe', label: 'Peru' },
  { value: 'ph', label: 'Philippines' },
  { value: 'pn', label: 'Pitcairn' },
  { value: 'pl', label: 'Poland' },
  { value: 'pt', label: 'Portugal' },
  { value: 'pr', label: 'Puerto Rico' },
  { value: 'qa', label: 'Qatar' },
  { value: 'cg', label: 'Republic of the Congo' },
  { value: 're', label: 'Reunion' },
  { value: 'ro', label: 'Romania' },
  { value: 'ru', label: 'Russia' },
  { value: 'rw', label: 'Rwanda' },
  { value: 'bl', label: 'Saint Barthelemy' },
  { value: 'sh', label: 'Saint Helena' },
  { value: 'kn', label: 'Saint Kitts and Nevis' },
  { value: 'lc', label: 'Saint Lucia' },
  { value: 'mf', label: 'Saint Martin' },
  { value: 'pm', label: 'Saint Pierre and Miquelon' },
  { value: 'vc', label: 'Saint Vincent and the Grenadines' },
  { value: 'ws', label: 'Samoa' },
  { value: 'sm', label: 'San Marino' },
  { value: 'st', label: 'Sao Tome and Principe' },
  { value: 'sa', label: 'Saudi Arabia' },
  { value: 'sn', label: 'Senegal' },
  { value: 'rs', label: 'Serbia' },
  { value: 'sc', label: 'Seychelles' },
  { value: 'sl', label: 'Sierra Leone' },
  { value: 'sg', label: 'Singapore' },
  { value: 'sx', label: 'Sint Maarten' },
  { value: 'sk', label: 'Slovakia' },
  { value: 'si', label: 'Slovenia' },
  { value: 'sb', label: 'Solomon Islands' },
  { value: 'so', label: 'Somalia' },
  { value: 'za', label: 'South Africa' },
  { value: 'gs', label: 'South Georgia and the South Sandwich Islands' },
  { value: 'kr', label: 'South Korea' },
  { value: 'ss', label: 'South Sudan' },
  { value: 'es', label: 'Spain' },
  { value: 'lk', label: 'Sri Lanka' },
  { value: 'sd', label: 'Sudan' },
  { value: 'sr', label: 'Suriname' },
  { value: 'sj', label: 'Svalbard and Jan Mayen' },
  { value: 'sz', label: 'Swaziland' },
  { value: 'se', label: 'Sweden' },
  { value: 'ch', label: 'Switzerland' },
  { value: 'sy', label: 'Syria' },
  { value: 'tw', label: 'Taiwan' },
  { value: 'tj', label: 'Tajikistan' },
  { value: 'tz', label: 'Tanzania' },
  { value: 'th', label: 'Thailand' },
  { value: 'tl', label: 'Timor-Leste' },
  { value: 'tg', label: 'Togo' },
  { value: 'tk', label: 'Tokelau' },
  { value: 'to', label: 'Tonga' },
  { value: 'tt', label: 'Trinidad and Tobago' },
  { value: 'tn', label: 'Tunisia' },
  { value: 'tr', label: 'Turkey' },
  { value: 'tm', label: 'Turkmenistan' },
  { value: 'tc', label: 'Turks and Caicos Islands' },
  { value: 'tv', label: 'Tuvalu' },
  { value: 'ug', label: 'Uganda' },
  { value: 'ua', label: 'Ukraine' },
  { value: 'ae', label: 'United Arab Emirates' },
  { value: 'gb', label: 'United Kingdom' },
  { value: 'us', label: 'United States' },
  { value: 'um', label: 'United States Minor Outlying Islands' },
  { value: 'uy', label: 'Uruguay' },
  { value: 'uz', label: 'Uzbekistan' },
  { value: 'vu', label: 'Vanuatu' },
  { value: 'va', label: 'Vatican' },
  { value: 've', label: 'Venezuela' },
  { value: 'vn', label: 'Vietnam' },
  { value: 'vi', label: 'Virgin Islands' },
  { value: 'wf', label: 'Wallis and Futuna' },
  { value: 'eh', label: 'Western Sahara' },
  { value: 'ye', label: 'Yemen' },
  { value: 'zm', label: 'Zambia' },
  { value: 'zw', label: 'Zimbabwe' },
];

const languages = [
    { value: 'ab', label: 'Abkhazian' },
    { value: 'aa', label: 'Afar' },
    { value: 'af', label: 'Afrikaans' },
    { value: 'ak', label: 'Akan' },
    { value: 'sq', label: 'Albanian' },
    { value: 'am', label: 'Amharic' },
    { value: 'ar', label: 'Arabic' },
    { value: 'an', label: 'Aragonese' },
    { value: 'hy', label: 'Armenian' },
    { value: 'as', label: 'Assamese' },
    { value: 'av', label: 'Avaric' },
    { value: 'ae', label: 'Avestan' },
    { value: 'ay', label: 'Aymara' },
    { value: 'az', label: 'Azerbaijani' },
    { value: 'bm', label: 'Bambara' },
    { value: 'ba', label: 'Bashkir' },
    { value: 'eu', label: 'Basque' },
    { value: 'be', label: 'Belarusian' },
    { value: 'bn', label: 'Bengali' },
    { value: 'bh', label: 'Bihari' },
    { value: 'bi', label: 'Bislama' },
    { value: 'bs', label: 'Bosnian' },
    { value: 'br', label: 'Breton' },
    { value: 'bg', label: 'Bulgarian' },
    { value: 'my', label: 'Burmese' },
    { value: 'ca', label: 'Catalan' },
    { value: 'ch', label: 'Chamorro' },
    { value: 'ce', label: 'Chechen' },
    { value: 'ny', label: 'Chichewa' },
    { value: 'zh', label: 'Chinese' },
    { value: 'cv', label: 'Chuvash' },
    { value: 'kw', label: 'Cornish' },
    { value: 'co', label: 'Corsican' },
    { value: 'cr', label: 'Cree' },
    { value: 'hr', label: 'Croatian' },
    { value: 'cs', label: 'Czech' },
    { value: 'da', label: 'Danish' },
    { value: 'dv', label: 'Divehi' },
    { value: 'nl', label: 'Dutch' },
    { value: 'dz', label: 'Dzongkha' },
    { value: 'en', label: 'English' },
    { value: 'eo', label: 'Esperanto' },
    { value: 'et', label: 'Estonian' },
    { value: 'ee', label: 'Ewe' },
    { value: 'fo', label: 'Faroese' },
    { value: 'fj', label: 'Fijian' },
    { value: 'fi', label: 'Finnish' },
    { value: 'fr', label: 'French' },
    { value: 'ff', label: 'Fulah' },
    { value: 'gl', label: 'Galician' },
    { value: 'ka', label: 'Georgian' },
    { value: 'de', label: 'German' },
    { value: 'el', label: 'Greek' },
    { value: 'gn', label: 'Guarani' },
    { value: 'gu', label: 'Gujarati' },
    { value: 'ht', label: 'Haitian' },
    { value: 'ha', label: 'Hausa' },
    { value: 'he', label: 'Hebrew' },
    { value: 'hz', label: 'Herero' },
    { value: 'hi', label: 'Hindi' },
    { value: 'ho', label: 'Hiri Motu' },
    { value: 'hu', label: 'Hungarian' },
    { value: 'ia', label: 'Interlingua' },
    { value: 'id', label: 'Indonesian' },
    { value: 'ie', label: 'Interlingue' },
    { value: 'ga', label: 'Irish' },
    { value: 'ig', label: 'Igbo' },
    { value: 'ik', label: 'Inupiaq' },
    { value: 'io', label: 'Ido' },
    { value: 'is', label: 'Icelandic' },
    { value: 'it', label: 'Italian' },
    { value: 'iu', label: 'Inuktitut' },
    { value: 'ja', label: 'Japanese' },
    { value: 'jv', label: 'Javanese' },
    { value: 'kl', label: 'Kalaallisut' },
    { value: 'kn', label: 'Kannada' },
    { value: 'kr', label: 'Kanuri' },
    { value: 'ks', label: 'Kashmiri' },
    { value: 'kk', label: 'Kazakh' },
    { value: 'km', label: 'Khmer' },
    { value: 'ki', label: 'Kikuyu' },
    { value: 'rw', label: 'Kinyarwanda' },
    { value: 'ky', label: 'Kyrgyz' },
    { value: 'kv', label: 'Komi' },
    { value: 'kg', label: 'Kongo' },
    { value: 'ko', label: 'Korean' },
    { value: 'ku', label: 'Kurdish' },
    { value: 'kj', label: 'Kwanyama' },
    { value: 'la', label: 'Latin' },
    { value: 'lb', label: 'Luxembourgish' },
    { value: 'lg', label: 'Luganda' },
    { value: 'li', label: 'Limburgish' },
    { value: 'ln', label: 'Lingala' },
    { value: 'lo', label: 'Lao' },
    { value: 'lt', label: 'Lithuanian' },
    { value: 'lu', label: 'Luba-Katanga' },
    { value: 'lv', label: 'Latvian' },
    { value: 'gv', label: 'Manx' },
    { value: 'mk', label: 'Macedonian' },
    { value: 'mg', label: 'Malagasy' },
    { value: 'ms', label: 'Malay' },
    { value: 'ml', label: 'Malayalam' },
    { value: 'mt', label: 'Maltese' },
    { value: 'mi', label: 'Maori' },
    { value: 'mr', label: 'Marathi' },
    { value: 'mh', label: 'Marshallese' },
    { value: 'mn', label: 'Mongolian' },
    { value: 'na', label: 'Nauru' },
    { value: 'nv', label: 'Navajo' },
    { value: 'nd', label: 'North Ndebele' },
    { value: 'ne', label: 'Nepali' },
    { value: 'ng', label: 'Ndonga' },
    { value: 'nb', label: 'Norwegian Bokmål' },
    { value: 'nn', label: 'Norwegian Nynorsk' },
    { value: 'no', label: 'Norwegian' },
    { value: 'ii', label: 'Nuosu' },
    { value: 'nr', label: 'South Ndebele' },
    { value: 'oc', label: 'Occitan' },
    { value: 'oj', label: 'Ojibwe' },
    { value: 'cu', label: 'Old Church Slavonic' },
    { value: 'om', label: 'Oromo' },
    { value: 'or', label: 'Oriya' },
    { value: 'os', label: 'Ossetian' },
    { value: 'pa', label: 'Panjabi' },
    { value: 'pi', label: 'Pali' },
    { value: 'fa', label: 'Persian' },
    { value: 'pl', label: 'Polish' },
    { value: 'ps', label: 'Pashto' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'qu', label: 'Quechua' },
    { value: 'rm', label: 'Romansh' },
    { value: 'rn', label: 'Kirundi' },
    { value: 'ro', label: 'Romanian' },
    { value: 'ru', label: 'Russian' },
    { value: 'sa', label: 'Sanskrit' },
    { value: 'sc', label: 'Sardinian' },
    { value: 'sd', label: 'Sindhi' },
    { value: 'se', label: 'Northern Sami' },
    { value: 'sm', label: 'Samoan' },
    { value: 'sg', label: 'Sango' },
    { value: 'sr', label: 'Serbian' },
    { value: 'gd', label: 'Scottish Gaelic' },
    { value: 'sn', label: 'Shona' },
    { value: 'si', label: 'Sinhala' },
    { value: 'sk', label: 'Slovak' },
    { value: 'sl', label: 'Slovenian' },
    { value: 'so', label: 'Somali' },
    { value: 'st', label: 'Southern Sotho' },
    { value: 'es', label: 'Spanish' },
    { value: 'su', label: 'Sundanese' },
    { value: 'sw', label: 'Swahili' },
    { value: 'ss', label: 'Swati' },
    { value: 'sv', label: 'Swedish' },
    { value: 'ta', label: 'Tamil' },
    { value: 'te', label: 'Telugu' },
    { value: 'tg', label: 'Tajik' },
    { value: 'th', label: 'Thai' },
    { value: 'ti', label: 'Tigrinya' },
    { value: 'bo', label: 'Tibetan' },
    { value: 'tk', label: 'Turkmen' },
    { value: 'tl', label: 'Tagalog' },
    { value: 'tn', label: 'T', },
    { value: 'to', label: 'Tonga' },
    { value: 'tr', label: 'Turkish' },
    { value: 'ts', label: 'Tsonga' },
    { value: 'tt', label: 'Tatar' },
    { value: 'tw', label: 'Twi' },
    { value: 'ty', label: 'Tahitian' },
    { value: 'ug', label: 'Uighur' },
    { value: 'uk', label: 'Ukrainian' },
    { value: 'ur', label: 'Urdu' },
    { value: 'uz', label: 'Uzbek' },
    { value: 've', label: 'Venda' },
    { value: 'vi', label: 'Vietnamese' },
    { value: 'vo', label: 'Volapük' },
    { value: 'wa', label: 'Walloon' },
    { value: 'cy', label: 'Welsh' },
    { value: 'wo', label: 'Wolof' },
    { value: 'fy', label: 'Western Frisian' },
    { value: 'xh', label: 'Xhosa' },
    { value: 'yi', label: 'Yiddish' },
    { value: 'yo', label: 'Yoruba' },
    { value: 'za', label: 'Zhuang' },
    { value: 'zu', label: 'Zulu' },
  ];

export { countries, languages };