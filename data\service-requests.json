[{"question": "dgfnb", "websiteType": "fxdg", "reference": "dzfg", "name": "zfdg", "phoneNumber": "zdfg", "email": "<EMAIL>", "brandName": "zdgf", "brandAge": "zdfg", "brandDescription": "zdgf", "hasDomain": "No", "hasHosting": "No", "needsLogo": "No", "categoryPages": "354", "needsContent": "No", "providingProductImages": "No", "additionalRequirements": "sdfzvcx", "budget": "zdfg", "status": "PENDING", "id": "SR1747478328308", "createdAt": "2025-05-17T10:38:48.308Z"}, {"question": "dgfnb", "websiteType": "fxdg", "reference": "dzfg", "name": "zfdg", "phoneNumber": "zdfg", "email": "<EMAIL>", "brandName": "zdgf", "brandAge": "zdfg", "brandDescription": "zdgf", "hasDomain": false, "hasHosting": false, "needsLogo": false, "categoryPages": "354", "needsContent": false, "providingProductImages": false, "additionalRequirements": "sdfzvcx", "budget": "zdfg", "status": "PENDING", "id": "SR1747481082675", "createdAt": "2025-05-17T11:24:42.675Z", "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "submissionCount": 1}, {"question": "afzdvcxb sdvxc", "websiteType": "szdzvfdf", "reference": "sdfdsf", "name": "sdf", "phoneNumber": "sdf", "email": "<EMAIL>", "brandName": "dfvcb", "brandAge": "zdgfdf", "brandDescription": "gdfzgzdfg", "hasDomain": false, "hasHosting": false, "needsLogo": false, "categoryPages": "54", "needsContent": false, "providingProductImages": false, "additionalRequirements": "dzfgzdfg", "budget": "zdfvzfdv", "status": "PENDING", "id": "SR1747482098303", "createdAt": "2025-05-17T11:41:38.303Z", "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "submissionCount": 2}]